# Datacenter Tycoon - iOS Game

A resource management idle/incremental game about datacenter operations and infrastructure management, built with Swift and SwiftUI.

## 🎮 Game Overview

**Genre:** Resource management idle/incremental game  
**Theme:** Datacenter operations and infrastructure management  
**Platform:** iOS (iPhone and iPad compatible)  
**Visual Style:** Isometric 2D view with clean, modern UI

## 🎯 Core Gameplay

### Resources
- **Power (kW):** Generated by power supplies, consumed by servers and cooling
- **Cooling (BTU):** Generated by cooling units, required to keep servers running
- **Network (Mbps):** Generated by network switches, affects server efficiency
- **Storage (TB):** Generated by storage arrays, affects data processing
- **Revenue ($):** Generated by servers when they have adequate power, cooling, and network

### Equipment Types
1. **Basic Server** - Generates revenue, consumes power and cooling
2. **Cooling Unit** - Generates cooling capacity
3. **Power Supply** - Generates power
4. **Network Switch** - Provides network bandwidth

### Equipment Tiers
- **Basic:** 1x efficiency, 1x cost
- **Advanced:** 2.5x efficiency, 4x cost (unlocked at $500 revenue)
- **Enterprise:** 6x efficiency, 15x cost (unlocked at $2000 revenue)
- **Quantum:** 15x efficiency, 50x cost (unlocked at higher levels)

## 🎮 How to Play

1. **Start:** Begin with $100 starting money and basic power/cooling
2. **Purchase Equipment:** Buy servers to generate revenue
3. **Balance Resources:** Ensure adequate power and cooling for servers
4. **Upgrade:** Purchase higher-tier equipment for better efficiency
5. **Expand:** Build larger datacenters with more equipment
6. **Idle Progression:** Resources generate even when app is closed (up to 8 hours)

### Controls
- **Tap empty cell:** Select position for equipment placement
- **Tap equipment:** Open upgrade/sell options
- **Tap servers:** Activate temporary revenue boost
- **Scroll:** Navigate the datacenter grid

## 🏗️ Project Structure

```
DatacenterGame/
├── DatacenterGameApp.swift          # Main app entry point
├── ContentView.swift                # Main game interface
├── Info.plist                      # App configuration
├── Models/
│   ├── GameState.swift             # Central game state management
│   ├── Resource.swift              # Resource types and calculations
│   ├── Equipment.swift             # Equipment definitions
│   └── DatacenterManager.swift     # Core game logic
├── Views/
│   ├── ResourceDisplayView.swift   # Resource counters UI
│   ├── GameView.swift              # Main gameplay grid
│   ├── EquipmentPurchaseView.swift # Equipment shop
│   └── EquipmentUpgradeView.swift  # Equipment upgrade interface
└── Utilities/
    ├── SaveManager.swift           # Data persistence
    ├── Constants.swift             # Game balance configuration
    └── VisualEffects.swift         # Animations and visual polish
```

## 🎨 Features

### Core Mechanics
- ✅ Idle/incremental resource generation
- ✅ Equipment purchase and upgrade system
- ✅ Resource balance and efficiency calculations
- ✅ Offline earnings (capped at 8 hours)
- ✅ Tap-to-boost mechanics
- ✅ Local data persistence

### Visual Design
- ✅ Isometric-style equipment grid
- ✅ Animated resource counters
- ✅ Responsive design (iPhone/iPad)
- ✅ Visual feedback and animations
- ✅ Color-coded resource types
- ✅ Equipment tier indicators

### Technical Features
- ✅ SwiftUI-based interface
- ✅ MVVM architecture
- ✅ Timer-based resource generation
- ✅ Background app refresh support
- ✅ Haptic feedback
- ✅ Auto-save functionality

## 🔧 Technical Requirements

- **iOS:** 15.0+
- **Xcode:** 15.0+
- **Swift:** 5.0+
- **Frameworks:** SwiftUI, Foundation, UIKit

## 🚀 Getting Started

1. **Open Project:** Open `DatacenterGame.xcodeproj` in Xcode
2. **Build:** Select your target device and build the project
3. **Run:** Launch the app on simulator or device
4. **Play:** Start building your datacenter empire!

## 🎮 Game Balance

### Starting Resources
- Revenue: $100
- Power: 10 kW
- Cooling: 10 BTU

### Equipment Costs (Basic Tier)
- Server: $50 (generates $2/s, consumes 1kW power + 1BTU cooling)
- Power Supply: $30 (generates 5kW power)
- Cooling Unit: $40 (generates 4BTU cooling, consumes 0.5kW power)
- Network Switch: $35 (generates 10Mbps network, consumes 0.3kW power)

### Progression
- Equipment efficiency scales with tier multipliers
- Costs increase exponentially to maintain challenge
- Unlock requirements prevent rushing to high-tier equipment
- Offline earnings capped to prevent exploitation

## 🎯 Future Enhancements

- [ ] Additional equipment types (storage arrays, backup generators)
- [ ] Datacenter expansion and multiple floors
- [ ] Achievement system
- [ ] Cloud integration for cross-device saves
- [ ] Prestige system for long-term progression
- [ ] Sound effects and music
- [ ] Tutorial system for new players

## 📱 Compatibility

- **iPhone:** All screen sizes supported with responsive layout
- **iPad:** Optimized for both portrait and landscape orientations
- **Accessibility:** VoiceOver support and dynamic type
- **Performance:** Optimized for smooth 60fps gameplay

## 🎮 Tips for Players

1. **Balance is Key:** Always ensure adequate power and cooling for servers
2. **Efficiency Matters:** Higher-tier equipment is more cost-effective long-term
3. **Plan Ahead:** Save money for equipment upgrades rather than buying many basic units
4. **Use Boosts:** Tap servers regularly for temporary revenue increases
5. **Offline Strategy:** Set up efficient resource generation before closing the app

---

**Datacenter Tycoon** - Build, manage, and optimize your virtual datacenter empire!
