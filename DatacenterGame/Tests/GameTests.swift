import XCTest
@testable import DatacenterGame

class GameTests: XCTestCase {
    
    var gameState: GameState!
    var datacenterManager: DatacenterManager!
    
    override func setUp() {
        super.setUp()
        gameState = GameState()
        datacenterManager = DatacenterManager()
        datacenterManager.gameState = gameState
    }
    
    override func tearDown() {
        gameState = nil
        datacenterManager = nil
        super.tearDown()
    }
    
    // MARK: - Game State Tests
    
    func testInitialGameState() {
        XCTAssertEqual(gameState.currentResources.revenue, 100, "Should start with $100")
        XCTAssertEqual(gameState.currentResources.power, 10, "Should start with 10kW power")
        XCTAssertEqual(gameState.currentResources.cooling, 10, "Should start with 10BTU cooling")
        XCTAssertEqual(gameState.placedEquipment.count, 0, "Should start with no equipment")
    }
    
    func testCanAfford() {
        XCTAssertTrue(gameState.canAfford(50), "Should be able to afford $50")
        XCTAssertFalse(gameState.canAfford(150), "Should not be able to afford $150")
    }
    
    func testCanPlaceEquipment() {
        let position = GridPosition(x: 0, y: 0)
        XCTAssertTrue(gameState.canPlaceEquipment(at: position), "Should be able to place at empty position")
        
        // Place equipment
        if let serverDef = GameConstants.getEquipmentDefinition(type: .server, tier: .basic) {
            _ = gameState.purchaseEquipment(serverDef, at: position)
            XCTAssertFalse(gameState.canPlaceEquipment(at: position), "Should not be able to place at occupied position")
        }
    }
    
    // MARK: - Equipment Tests
    
    func testEquipmentPurchase() {
        guard let serverDef = GameConstants.getEquipmentDefinition(type: .server, tier: .basic) else {
            XCTFail("Server definition should exist")
            return
        }
        
        let initialRevenue = gameState.currentResources.revenue
        let position = GridPosition(x: 0, y: 0)
        
        let success = gameState.purchaseEquipment(serverDef, at: position)
        
        XCTAssertTrue(success, "Equipment purchase should succeed")
        XCTAssertEqual(gameState.currentResources.revenue, initialRevenue - serverDef.cost, "Revenue should decrease by equipment cost")
        XCTAssertEqual(gameState.placedEquipment.count, 1, "Should have one piece of equipment")
        XCTAssertEqual(gameState.getEquipment(at: position)?.definition.type, .server, "Should be a server at the position")
    }
    
    func testEquipmentPurchaseInsufficientFunds() {
        // Spend all money
        gameState.currentResources.revenue = 0
        
        guard let serverDef = GameConstants.getEquipmentDefinition(type: .server, tier: .basic) else {
            XCTFail("Server definition should exist")
            return
        }
        
        let position = GridPosition(x: 0, y: 0)
        let success = gameState.purchaseEquipment(serverDef, at: position)
        
        XCTAssertFalse(success, "Equipment purchase should fail with insufficient funds")
        XCTAssertEqual(gameState.placedEquipment.count, 0, "Should have no equipment")
    }
    
    // MARK: - Resource Calculation Tests
    
    func testResourceProduction() {
        guard let serverDef = GameConstants.getEquipmentDefinition(type: .server, tier: .basic) else {
            XCTFail("Server definition should exist")
            return
        }
        
        // Place a server
        _ = gameState.purchaseEquipment(serverDef, at: GridPosition(x: 0, y: 0))
        
        let production = gameState.calculateTotalProduction()
        XCTAssertEqual(production.revenue, serverDef.actualProduction.revenue, "Should produce expected revenue")
    }
    
    func testResourceConsumption() {
        guard let serverDef = GameConstants.getEquipmentDefinition(type: .server, tier: .basic) else {
            XCTFail("Server definition should exist")
            return
        }
        
        // Place a server
        _ = gameState.purchaseEquipment(serverDef, at: GridPosition(x: 0, y: 0))
        
        let consumption = gameState.calculateTotalConsumption()
        XCTAssertEqual(consumption.power, serverDef.actualConsumption.power, "Should consume expected power")
        XCTAssertEqual(consumption.cooling, serverDef.actualConsumption.cooling, "Should consume expected cooling")
    }
    
    // MARK: - Equipment Tier Tests
    
    func testEquipmentTierMultipliers() {
        XCTAssertEqual(EquipmentTier.basic.multiplier, 1.0, "Basic tier should have 1x multiplier")
        XCTAssertEqual(EquipmentTier.advanced.multiplier, 2.5, "Advanced tier should have 2.5x multiplier")
        XCTAssertEqual(EquipmentTier.enterprise.multiplier, 6.0, "Enterprise tier should have 6x multiplier")
        XCTAssertEqual(EquipmentTier.quantum.multiplier, 15.0, "Quantum tier should have 15x multiplier")
    }
    
    func testEquipmentCostScaling() {
        guard let basicServer = GameConstants.getEquipmentDefinition(type: .server, tier: .basic),
              let advancedServer = GameConstants.getEquipmentDefinition(type: .server, tier: .advanced) else {
            XCTFail("Equipment definitions should exist")
            return
        }
        
        XCTAssertGreaterThan(advancedServer.cost, basicServer.cost, "Advanced equipment should cost more than basic")
        XCTAssertEqual(advancedServer.cost, basicServer.baseCost * EquipmentTier.advanced.costMultiplier, "Cost should scale correctly")
    }
    
    // MARK: - Save/Load Tests
    
    func testSaveAndLoad() {
        // Modify game state
        gameState.currentResources.revenue = 500
        if let serverDef = GameConstants.getEquipmentDefinition(type: .server, tier: .basic) {
            _ = gameState.purchaseEquipment(serverDef, at: GridPosition(x: 0, y: 0))
        }
        
        // Save
        SaveManager.shared.saveGameState(gameState)
        
        // Create new game state and load
        let newGameState = GameState()
        SaveManager.shared.loadGameState(newGameState)
        
        XCTAssertEqual(newGameState.currentResources.revenue, 450, "Revenue should be saved and loaded correctly") // 500 - 50 (server cost)
        XCTAssertEqual(newGameState.placedEquipment.count, 1, "Equipment should be saved and loaded correctly")
    }
    
    // MARK: - Game Balance Tests
    
    func testGameProgression() {
        // Test that basic equipment can be afforded initially
        let availableEquipment = GameConstants.getAvailableEquipment(for: gameState)
        let basicEquipment = availableEquipment.filter { $0.tier == .basic }
        
        XCTAssertGreaterThan(basicEquipment.count, 0, "Should have basic equipment available initially")
        
        // Test that advanced equipment requires progression
        gameState.currentResources.revenue = 100 // Reset to starting amount
        let advancedEquipment = availableEquipment.filter { $0.tier == .advanced }
        XCTAssertEqual(advancedEquipment.count, 0, "Should not have advanced equipment available initially")
        
        // Increase revenue and test again
        gameState.currentResources.revenue = 1000
        let newAvailableEquipment = GameConstants.getAvailableEquipment(for: gameState)
        let newAdvancedEquipment = newAvailableEquipment.filter { $0.tier == .advanced }
        XCTAssertGreaterThan(newAdvancedEquipment.count, 0, "Should have advanced equipment available with sufficient revenue")
    }
    
    // MARK: - Performance Tests
    
    func testResourceUpdatePerformance() {
        // Place multiple pieces of equipment
        for x in 0..<5 {
            for y in 0..<5 {
                if let serverDef = GameConstants.getEquipmentDefinition(type: .server, tier: .basic) {
                    gameState.currentResources.revenue = 1000 // Ensure we can afford it
                    _ = gameState.purchaseEquipment(serverDef, at: GridPosition(x: x, y: y))
                }
            }
        }
        
        // Measure performance of resource calculations
        measure {
            for _ in 0..<1000 {
                _ = gameState.calculateTotalProduction()
                _ = gameState.calculateTotalConsumption()
            }
        }
    }
}

// MARK: - Test Utilities

extension GameTests {
    
    func createTestGameState(withRevenue revenue: Double) -> GameState {
        let state = GameState()
        state.currentResources.revenue = revenue
        return state
    }
    
    func placeTestEquipment(type: EquipmentType, tier: EquipmentTier, at position: GridPosition) -> Bool {
        guard let definition = GameConstants.getEquipmentDefinition(type: type, tier: tier) else {
            return false
        }
        
        gameState.currentResources.revenue = 10000 // Ensure we can afford it
        return gameState.purchaseEquipment(definition, at: position)
    }
}
