import SwiftUI

struct EquipmentPurchaseView: View {
    @EnvironmentObject var gameState: GameState
    @EnvironmentObject var datacenterManager: DatacenterManager
    @State private var selectedEquipment: EquipmentDefinition?
    @State private var showingEquipmentDetails = false
    
    var availableEquipment: [EquipmentDefinition] {
        datacenterManager.getAvailableEquipment()
    }
    
    var body: some View {
        VStack(spacing: 8) {
            // Header
            HStack {
                Text("Equipment Shop")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button(action: {
                    showingEquipmentDetails.toggle()
                }) {
                    Image(systemName: "info.circle")
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal)
            
            // Equipment grid
            ScrollView(.horizontal, showsIndicators: false) {
                LazyHGrid(rows: [GridItem(.flexible())], spacing: 12) {
                    ForEach(availableEquipment, id: \.type.rawValue) { equipment in
                        EquipmentPurchaseCard(
                            equipment: equipment,
                            isSelected: selectedEquipment?.type == equipment.type && selectedEquipment?.tier == equipment.tier,
                            canAfford: gameState.canAfford(equipment.cost)
                        )
                        .onTapGesture {
                            handleEquipmentSelection(equipment)
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .sheet(isPresented: $showingEquipmentDetails) {
            EquipmentDetailsView(equipment: selectedEquipment)
        }
    }
    
    private func handleEquipmentSelection(_ equipment: EquipmentDefinition) {
        if selectedEquipment?.type == equipment.type && selectedEquipment?.tier == equipment.tier {
            // Deselect if already selected
            selectedEquipment = nil
        } else {
            // Select new equipment
            selectedEquipment = equipment
            
            // Try to place equipment at first available position
            if let position = findFirstAvailablePosition() {
                let success = datacenterManager.purchaseEquipment(equipment, at: position)
                if success {
                    selectedEquipment = nil // Deselect after successful purchase
                }
            }
        }
    }
    
    private func findFirstAvailablePosition() -> GridPosition? {
        for row in 0..<gameState.gridSize.height {
            for col in 0..<gameState.gridSize.width {
                let position = GridPosition(x: col, y: row)
                if gameState.canPlaceEquipment(at: position) {
                    return position
                }
            }
        }
        return nil
    }
}

struct EquipmentPurchaseCard: View {
    let equipment: EquipmentDefinition
    let isSelected: Bool
    let canAfford: Bool
    
    var body: some View {
        VStack(spacing: 6) {
            // Equipment icon and tier
            ZStack {
                Circle()
                    .fill(equipment.type.color.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: equipment.type.icon)
                    .foregroundColor(equipment.type.color)
                    .font(.system(size: 18, weight: .semibold))
                
                // Tier badge
                Text(equipment.tier.displayName.prefix(1))
                    .font(.system(size: 8, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 14, height: 14)
                    .background(Circle().fill(equipment.type.color))
                    .offset(x: 15, y: -15)
            }
            
            // Equipment name
            Text(equipment.type.displayName)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.primary)
                .lineLimit(1)
            
            // Cost
            HStack(spacing: 2) {
                Image(systemName: "dollarsign.circle.fill")
                    .foregroundColor(.orange)
                    .font(.system(size: 8))
                
                Text(GameConstants.formatNumber(equipment.cost))
                    .font(.system(size: 10, weight: .bold, design: .monospaced))
                    .foregroundColor(canAfford ? .primary : .red)
            }
            
            // Production/Consumption indicators
            VStack(spacing: 2) {
                if equipment.actualProduction.revenue > 0 {
                    ProductionIndicator(
                        icon: "plus.circle.fill",
                        value: equipment.actualProduction.revenue,
                        color: .green,
                        unit: "$/s"
                    )
                }
                
                if equipment.actualConsumption.power > 0 {
                    ProductionIndicator(
                        icon: "minus.circle.fill",
                        value: equipment.actualConsumption.power,
                        color: .red,
                        unit: "kW"
                    )
                }
            }
        }
        .frame(width: 80, height: 100)
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? equipment.type.color.opacity(0.2) : Color.gray.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            isSelected ? equipment.type.color : Color.gray.opacity(0.3),
                            lineWidth: isSelected ? 2 : 1
                        )
                )
        )
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .opacity(canAfford ? 1.0 : 0.6)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
        .animation(.easeInOut(duration: 0.2), value: canAfford)
    }
}

struct ProductionIndicator: View {
    let icon: String
    let value: Double
    let color: Color
    let unit: String
    
    var body: some View {
        HStack(spacing: 2) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.system(size: 6))
            
            Text("\(GameConstants.formatNumber(value))\(unit)")
                .font(.system(size: 6, weight: .medium))
                .foregroundColor(.secondary)
        }
    }
}

struct EquipmentDetailsView: View {
    let equipment: EquipmentDefinition?
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: 16) {
                if let equipment = equipment {
                    // Equipment header
                    HStack {
                        Image(systemName: equipment.type.icon)
                            .foregroundColor(equipment.type.color)
                            .font(.system(size: 32, weight: .semibold))
                        
                        VStack(alignment: .leading) {
                            Text(equipment.displayName)
                                .font(.title2)
                                .fontWeight(.bold)
                            
                            Text("Cost: $\(GameConstants.formatNumber(equipment.cost))")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    
                    Divider()
                    
                    // Production details
                    if equipment.actualProduction.revenue > 0 {
                        DetailRow(
                            title: "Revenue Generation",
                            value: "\(GameConstants.formatNumber(equipment.actualProduction.revenue))/s",
                            color: .green
                        )
                    }
                    
                    // Consumption details
                    if equipment.actualConsumption.power > 0 {
                        DetailRow(
                            title: "Power Consumption",
                            value: "\(GameConstants.formatNumber(equipment.actualConsumption.power)) kW",
                            color: .red
                        )
                    }
                    
                    if equipment.actualConsumption.cooling > 0 {
                        DetailRow(
                            title: "Cooling Required",
                            value: "\(GameConstants.formatNumber(equipment.actualConsumption.cooling)) BTU",
                            color: .red
                        )
                    }
                    
                    Spacer()
                } else {
                    Text("No equipment selected")
                        .foregroundColor(.secondary)
                    Spacer()
                }
            }
            .padding()
            .navigationTitle("Equipment Details")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct DetailRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.body)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(value)
                .font(.body)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
        .padding(.vertical, 4)
    }
}

struct EquipmentPurchaseView_Previews: PreviewProvider {
    static var previews: some View {
        let gameState = GameState()
        gameState.currentResources.revenue = 500
        
        return EquipmentPurchaseView()
            .environmentObject(gameState)
            .environmentObject(DatacenterManager())
            .previewLayout(.sizeThatFits)
    }
}
