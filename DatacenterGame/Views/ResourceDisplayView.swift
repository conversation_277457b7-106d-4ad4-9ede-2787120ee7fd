import SwiftUI

struct ResourceDisplayView: View {
    @EnvironmentObject var gameState: GameState
    @EnvironmentObject var datacenterManager: DatacenterManager
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(ResourceType.allCases, id: \.self) { resourceType in
                ResourceCard(
                    resourceType: resourceType,
                    amount: gameState.currentResources[resourceType],
                    isBoostActive: datacenterManager.isBoostActive && resourceType == .revenue
                )
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
}

struct ResourceCard: View {
    let resourceType: ResourceType
    let amount: Double
    let isBoostActive: Bool
    
    var body: some View {
        VStack(spacing: 4) {
            HStack(spacing: 4) {
                Image(systemName: resourceType.icon)
                    .foregroundColor(resourceType.color)
                    .font(.system(size: 14, weight: .semibold))
                
                if isBoostActive {
                    Image(systemName: "bolt.fill")
                        .foregroundColor(.yellow)
                        .font(.system(size: 10))
                        .scaleEffect(isBoostActive ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isBoostActive)
                }
            }
            
            AnimatedNumber(value: amount)
                .font(.system(size: 12, weight: .bold, design: .monospaced))
                .foregroundColor(.primary)
                .lineLimit(1)
                .minimumScaleFactor(0.8)
            
            Text(resourceType.unit)
                .font(.system(size: 8, weight: .medium))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .padding(.horizontal, 6)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(resourceType.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(resourceType.color.opacity(0.3), lineWidth: 1)
                )
        )
        .scaleEffect(isBoostActive ? 1.05 : 1.0)
        .glow(color: resourceType.color, radius: isBoostActive ? 8 : 0)
        .animation(.easeInOut(duration: 0.2), value: amount)
        .animation(.easeInOut(duration: 0.3), value: isBoostActive)
    }
}

struct ResourceDisplayView_Previews: PreviewProvider {
    static var previews: some View {
        let gameState = GameState()
        gameState.currentResources = ResourceAmount(
            power: 150.5,
            cooling: 89.2,
            network: 1250.0,
            storage: 45.8,
            revenue: 2847.3
        )
        
        return ResourceDisplayView()
            .environmentObject(gameState)
            .environmentObject(DatacenterManager())
            .previewLayout(.sizeThatFits)
            .padding()
    }
}
