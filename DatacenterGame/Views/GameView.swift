import SwiftUI

struct GameView: View {
    @EnvironmentObject var gameState: GameState
    @EnvironmentObject var datacenterManager: DatacenterManager
    @State private var selectedPosition: GridPosition?
    @State private var showingUpgradeView = false
    @State private var selectedEquipment: PlacedEquipment?
    
    var body: some View {
        GeometryReader { geometry in
            let cellSize = ResponsiveLayout.cellSize(for: geometry.size)
            let columns = ResponsiveLayout.gridColumns(for: geometry.size)

            ScrollView([.horizontal, .vertical], showsIndicators: false) {
                LazyVGrid(columns: Array(repeating: GridItem(.fixed(cellSize), spacing: 4), count: min(columns, gameState.gridSize.width)), spacing: 4) {
                    ForEach(0..<gameState.gridSize.height, id: \.self) { row in
                        ForEach(0..<gameState.gridSize.width, id: \.self) { col in
                            let position = GridPosition(x: col, y: row)
                            GridCell(
                                position: position,
                                equipment: gameState.getEquipment(at: position),
                                isSelected: selectedPosition == position,
                                cellSize: cellSize
                            )
                            .onTapGesture {
                                handleCellTap(at: position)
                            }
                        }
                    }
                }
                .padding()
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.gray.opacity(0.1),
                        Color.blue.opacity(0.05)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .sheet(isPresented: $showingUpgradeView) {
                if let equipment = selectedEquipment {
                    EquipmentUpgradeView(equipment: equipment)
                }
            }
        }
    }
    
    private func handleCellTap(at position: GridPosition) {
        if let equipment = gameState.getEquipment(at: position) {
            // Tap on existing equipment - show upgrade options
            selectedEquipment = equipment
            showingUpgradeView = true
        } else {
            // Tap on empty cell - select for placement
            selectedPosition = selectedPosition == position ? nil : position
        }
    }
}

struct GridCell: View {
    let position: GridPosition
    let equipment: PlacedEquipment?
    let isSelected: Bool
    let cellSize: CGFloat
    
    var body: some View {
        ZStack {
            // Cell background
            RoundedRectangle(cornerRadius: 8)
                .fill(cellBackgroundColor)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(cellBorderColor, lineWidth: isSelected ? 2 : 1)
                )
            
            // Equipment or empty state
            if let equipment = equipment {
                EquipmentView(equipment: equipment)
            } else {
                // Empty cell indicator
                Image(systemName: "plus.circle.dashed")
                    .foregroundColor(.gray.opacity(0.5))
                    .font(.system(size: 20))
                    .opacity(isSelected ? 1.0 : 0.3)
            }
        }
        .frame(width: cellSize, height: cellSize)
        .scaleEffect(isSelected ? 1.1 : 1.0)
        .animation(.easeInOut(duration: GameConstants.equipmentAnimationDuration), value: isSelected)
        .animation(.easeInOut(duration: GameConstants.equipmentAnimationDuration), value: equipment?.id)
    }
    
    private var cellBackgroundColor: Color {
        if isSelected {
            return Color.blue.opacity(0.2)
        } else if equipment != nil {
            return Color.white.opacity(0.8)
        } else {
            return Color.gray.opacity(0.1)
        }
    }
    
    private var cellBorderColor: Color {
        if isSelected {
            return Color.blue
        } else if equipment != nil {
            return equipment?.definition.type.color ?? Color.gray
        } else {
            return Color.gray.opacity(0.3)
        }
    }
}

struct EquipmentView: View {
    let equipment: PlacedEquipment
    
    var body: some View {
        VStack(spacing: 2) {
            // Equipment icon
            Image(systemName: equipment.definition.type.icon)
                .foregroundColor(equipment.definition.type.color)
                .font(.system(size: 16, weight: .semibold))
            
            // Tier indicator
            Text(equipment.definition.tier.displayName.prefix(1))
                .font(.system(size: 8, weight: .bold))
                .foregroundColor(.white)
                .frame(width: 12, height: 12)
                .background(
                    Circle()
                        .fill(equipment.definition.type.color)
                )
            
            // Operational status
            if !equipment.isOperational {
                ProgressView()
                    .scaleEffect(0.5)
            }
        }
        .opacity(equipment.isOperational ? 1.0 : 0.6)
        .scaleEffect(equipment.isOperational ? 1.0 : 0.9)
        .animation(.easeInOut(duration: 0.5), value: equipment.isOperational)
    }
}

struct GameView_Previews: PreviewProvider {
    static var previews: some View {
        let gameState = GameState()
        
        // Add some sample equipment
        if let serverDef = GameConstants.getEquipmentDefinition(type: .server, tier: .basic) {
            _ = gameState.purchaseEquipment(serverDef, at: GridPosition(x: 1, y: 1))
        }
        if let powerDef = GameConstants.getEquipmentDefinition(type: .powerSupply, tier: .basic) {
            _ = gameState.purchaseEquipment(powerDef, at: GridPosition(x: 2, y: 1))
        }
        
        return GameView()
            .environmentObject(gameState)
            .environmentObject(DatacenterManager())
    }
}
