import SwiftUI

struct EquipmentUpgradeView: View {
    @EnvironmentObject var gameState: GameState
    @EnvironmentObject var datacenterManager: DatacenterManager
    let equipment: PlacedEquipment
    @Environment(\.presentationMode) var presentationMode
    
    var nextTierEquipment: EquipmentDefinition? {
        let currentTierIndex = EquipmentTier.allCases.firstIndex(of: equipment.definition.tier) ?? 0
        guard currentTierIndex < EquipmentTier.allCases.count - 1 else { return nil }
        
        let nextTier = EquipmentTier.allCases[currentTierIndex + 1]
        return GameConstants.getEquipmentDefinition(type: equipment.definition.type, tier: nextTier)
    }
    
    var upgradeCost: Double {
        guard let nextTier = nextTierEquipment else { return 0 }
        return nextTier.cost - equipment.definition.cost * 0.5 // 50% trade-in value
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Current equipment info
                CurrentEquipmentCard(equipment: equipment)
                
                // Upgrade arrow
                if nextTierEquipment != nil {
                    Image(systemName: "arrow.down.circle.fill")
                        .foregroundColor(.blue)
                        .font(.system(size: 24))
                }
                
                // Next tier equipment info
                if let nextTier = nextTierEquipment {
                    NextTierEquipmentCard(equipment: nextTier, upgradeCost: upgradeCost)
                    
                    // Upgrade button
                    Button(action: performUpgrade) {
                        HStack {
                            Image(systemName: "arrow.up.circle.fill")
                            Text("Upgrade for $\(GameConstants.formatNumber(upgradeCost))")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(gameState.canAfford(upgradeCost) ? Color.blue : Color.gray)
                        )
                    }
                    .disabled(!gameState.canAfford(upgradeCost))
                } else {
                    VStack {
                        Image(systemName: "star.fill")
                            .foregroundColor(.gold)
                            .font(.system(size: 32))
                        
                        Text("Maximum Tier Reached")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        
                        Text("This equipment is already at its highest tier.")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                }
                
                Spacer()
                
                // Sell button
                Button(action: sellEquipment) {
                    HStack {
                        Image(systemName: "trash.circle.fill")
                        Text("Sell for $\(GameConstants.formatNumber(equipment.definition.cost * 0.3))")
                    }
                    .font(.subheadline)
                    .foregroundColor(.red)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.red, lineWidth: 1)
                    )
                }
            }
            .padding()
            .navigationTitle("Equipment Upgrade")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
    }
    
    private func performUpgrade() {
        guard let nextTier = nextTierEquipment,
              gameState.canAfford(upgradeCost) else { return }
        
        // Remove current equipment
        gameState.removeEquipment(at: equipment.position)
        
        // Deduct upgrade cost
        gameState.currentResources.revenue -= upgradeCost
        
        // Place upgraded equipment
        let upgradedEquipment = PlacedEquipment(definition: nextTier, position: equipment.position)
        gameState.placedEquipment.append(upgradedEquipment)
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
        
        presentationMode.wrappedValue.dismiss()
    }
    
    private func sellEquipment() {
        let sellValue = equipment.definition.cost * 0.3 // 30% sell value
        
        // Remove equipment
        gameState.removeEquipment(at: equipment.position)
        
        // Add sell value to revenue
        gameState.currentResources.revenue += sellValue
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        presentationMode.wrappedValue.dismiss()
    }
}

struct CurrentEquipmentCard: View {
    let equipment: PlacedEquipment
    
    var body: some View {
        VStack(spacing: 12) {
            Text("Current Equipment")
                .font(.headline)
                .foregroundColor(.secondary)
            
            HStack {
                Image(systemName: equipment.definition.type.icon)
                    .foregroundColor(equipment.definition.type.color)
                    .font(.system(size: 32))
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(equipment.definition.displayName)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    if equipment.definition.actualProduction.revenue > 0 {
                        Text("Generates: $\(GameConstants.formatNumber(equipment.definition.actualProduction.revenue))/s")
                            .font(.subheadline)
                            .foregroundColor(.green)
                    }
                    
                    if equipment.definition.actualConsumption.power > 0 {
                        Text("Consumes: \(GameConstants.formatNumber(equipment.definition.actualConsumption.power)) kW")
                            .font(.subheadline)
                            .foregroundColor(.red)
                    }
                }
                
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
    }
}

struct NextTierEquipmentCard: View {
    let equipment: EquipmentDefinition
    let upgradeCost: Double
    
    var body: some View {
        VStack(spacing: 12) {
            Text("Upgraded Equipment")
                .font(.headline)
                .foregroundColor(.blue)
            
            HStack {
                Image(systemName: equipment.type.icon)
                    .foregroundColor(equipment.type.color)
                    .font(.system(size: 32))
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(equipment.displayName)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    if equipment.actualProduction.revenue > 0 {
                        Text("Generates: $\(GameConstants.formatNumber(equipment.actualProduction.revenue))/s")
                            .font(.subheadline)
                            .foregroundColor(.green)
                    }
                    
                    if equipment.actualConsumption.power > 0 {
                        Text("Consumes: \(GameConstants.formatNumber(equipment.actualConsumption.power)) kW")
                            .font(.subheadline)
                            .foregroundColor(.red)
                    }
                }
                
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.blue.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

extension Color {
    static let gold = Color(red: 1.0, green: 0.84, blue: 0.0)
}

struct EquipmentUpgradeView_Previews: PreviewProvider {
    static var previews: some View {
        let gameState = GameState()
        gameState.currentResources.revenue = 1000
        
        let equipment = PlacedEquipment(
            definition: GameConstants.getEquipmentDefinition(type: .server, tier: .basic)!,
            position: GridPosition(x: 0, y: 0)
        )
        
        return EquipmentUpgradeView(equipment: equipment)
            .environmentObject(gameState)
            .environmentObject(DatacenterManager())
    }
}
