import Foundation

struct GameConstants {
    
    // MARK: - Equipment Definitions
    static let equipmentDefinitions: [EquipmentDefinition] = [
        // Basic Tier
        EquipmentDefinition(
            type: .server,
            tier: .basic,
            baseCost: 50,
            produces: ResourceAmount(revenue: 2.0),
            consumes: ResourceAmount(power: 1.0, cooling: 1.0),
            unlockRequirement: ResourceAmount()
        ),
        EquipmentDefinition(
            type: .powerSupply,
            tier: .basic,
            baseCost: 30,
            produces: ResourceAmount(power: 5.0),
            consumes: ResourceAmount(),
            unlockRequirement: ResourceAmount()
        ),
        EquipmentDefinition(
            type: .coolingUnit,
            tier: .basic,
            baseCost: 40,
            produces: ResourceAmount(cooling: 4.0),
            consumes: ResourceAmount(power: 0.5),
            unlockRequirement: ResourceAmount()
        ),
        EquipmentDefinition(
            type: .networkSwitch,
            tier: .basic,
            baseCost: 35,
            produces: ResourceAmount(network: 10.0),
            consumes: ResourceAmount(power: 0.3),
            unlockRequirement: ResourceAmount()
        ),
        
        // Advanced Tier
        EquipmentDefinition(
            type: .server,
            tier: .advanced,
            baseCost: 50,
            produces: ResourceAmount(revenue: 2.0),
            consumes: ResourceAmount(power: 1.0, cooling: 1.0),
            unlockRequirement: ResourceAmount(revenue: 500)
        ),
        EquipmentDefinition(
            type: .powerSupply,
            tier: .advanced,
            baseCost: 30,
            produces: ResourceAmount(power: 5.0),
            consumes: ResourceAmount(),
            unlockRequirement: ResourceAmount(revenue: 300)
        ),
        EquipmentDefinition(
            type: .coolingUnit,
            tier: .advanced,
            baseCost: 40,
            produces: ResourceAmount(cooling: 4.0),
            consumes: ResourceAmount(power: 0.5),
            unlockRequirement: ResourceAmount(revenue: 400)
        ),
        EquipmentDefinition(
            type: .networkSwitch,
            tier: .advanced,
            baseCost: 35,
            produces: ResourceAmount(network: 10.0),
            consumes: ResourceAmount(power: 0.3),
            unlockRequirement: ResourceAmount(revenue: 350)
        ),
        
        // Enterprise Tier
        EquipmentDefinition(
            type: .server,
            tier: .enterprise,
            baseCost: 50,
            produces: ResourceAmount(revenue: 2.0),
            consumes: ResourceAmount(power: 1.0, cooling: 1.0),
            unlockRequirement: ResourceAmount(revenue: 2000)
        ),
        EquipmentDefinition(
            type: .powerSupply,
            tier: .enterprise,
            baseCost: 30,
            produces: ResourceAmount(power: 5.0),
            consumes: ResourceAmount(),
            unlockRequirement: ResourceAmount(revenue: 1500)
        ),
        EquipmentDefinition(
            type: .coolingUnit,
            tier: .enterprise,
            baseCost: 40,
            produces: ResourceAmount(cooling: 4.0),
            consumes: ResourceAmount(power: 0.5),
            unlockRequirement: ResourceAmount(revenue: 1800)
        ),
        EquipmentDefinition(
            type: .networkSwitch,
            tier: .enterprise,
            baseCost: 35,
            produces: ResourceAmount(network: 10.0),
            consumes: ResourceAmount(power: 0.3),
            unlockRequirement: ResourceAmount(revenue: 1600)
        )
    ]
    
    // MARK: - Game Balance
    static let resourceGenerationInterval: TimeInterval = 1.0 // Generate resources every second
    static let maxOfflineHours: Double = 8.0 // Maximum offline earnings (8 hours)
    static let tapBoostMultiplier: Double = 2.0 // Multiplier for tap-to-boost
    static let tapBoostDuration: TimeInterval = 5.0 // Duration of tap boost in seconds
    
    // MARK: - UI Constants
    static let gridCellSize: CGFloat = 60
    static let equipmentAnimationDuration: Double = 0.3
    static let resourceUpdateAnimationDuration: Double = 0.2
    
    // MARK: - Helper Methods
    static func getEquipmentDefinition(type: EquipmentType, tier: EquipmentTier) -> EquipmentDefinition? {
        return equipmentDefinitions.first { $0.type == type && $0.tier == tier }
    }
    
    static func getAvailableEquipment(for gameState: GameState) -> [EquipmentDefinition] {
        return equipmentDefinitions.filter { definition in
            gameState.currentResources.revenue >= definition.unlockRequirement.revenue
        }
    }
    
    static func formatNumber(_ number: Double) -> String {
        if number >= 1_000_000 {
            return String(format: "%.1fM", number / 1_000_000)
        } else if number >= 1_000 {
            return String(format: "%.1fK", number / 1_000)
        } else {
            return String(format: "%.0f", number)
        }
    }
}
