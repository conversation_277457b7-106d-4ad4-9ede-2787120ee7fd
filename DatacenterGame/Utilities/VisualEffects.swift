import SwiftUI

// MARK: - Isometric Transform Modifier
struct IsometricTransform: ViewModifier {
    let depth: CGFloat
    
    func body(content: Content) -> some View {
        content
            .rotation3DEffect(
                .degrees(60),
                axis: (x: 1, y: 0, z: 0)
            )
            .rotation3DEffect(
                .degrees(45),
                axis: (x: 0, y: 1, z: 0)
            )
            .scaleEffect(x: 1.0, y: 0.8)
            .offset(y: depth * 0.3)
    }
}

extension View {
    func isometric(depth: CGFloat = 0) -> some View {
        modifier(IsometricTransform(depth: depth))
    }
}

// MARK: - Pulse Animation
struct PulseEffect: ViewModifier {
    @State private var isPulsing = false
    let duration: Double
    let minScale: CGFloat
    let maxScale: CGFloat
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPulsing ? maxScale : minScale)
            .animation(
                Animation.easeInOut(duration: duration)
                    .repeatForever(autoreverses: true),
                value: isPulsing
            )
            .onAppear {
                isPulsing = true
            }
    }
}

extension View {
    func pulse(duration: Double = 1.0, minScale: CGFloat = 0.95, maxScale: CGFloat = 1.05) -> some View {
        modifier(PulseEffect(duration: duration, minScale: minScale, maxScale: maxScale))
    }
}

// MARK: - Glow Effect
struct GlowEffect: ViewModifier {
    let color: Color
    let radius: CGFloat
    
    func body(content: Content) -> some View {
        content
            .shadow(color: color.opacity(0.6), radius: radius, x: 0, y: 0)
            .shadow(color: color.opacity(0.3), radius: radius * 2, x: 0, y: 0)
    }
}

extension View {
    func glow(color: Color, radius: CGFloat = 10) -> some View {
        modifier(GlowEffect(color: color, radius: radius))
    }
}

// MARK: - Floating Animation
struct FloatingEffect: ViewModifier {
    @State private var isFloating = false
    let amplitude: CGFloat
    let duration: Double
    
    func body(content: Content) -> some View {
        content
            .offset(y: isFloating ? amplitude : -amplitude)
            .animation(
                Animation.easeInOut(duration: duration)
                    .repeatForever(autoreverses: true),
                value: isFloating
            )
            .onAppear {
                isFloating = true
            }
    }
}

extension View {
    func floating(amplitude: CGFloat = 5, duration: Double = 2.0) -> some View {
        modifier(FloatingEffect(amplitude: amplitude, duration: duration))
    }
}

// MARK: - Shimmer Effect
struct ShimmerEffect: ViewModifier {
    @State private var phase: CGFloat = 0
    let duration: Double
    let bounce: Bool
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Rectangle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.clear,
                                Color.white.opacity(0.4),
                                Color.clear
                            ]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .rotationEffect(.degrees(30))
                    .offset(x: phase)
                    .clipped()
            )
            .onAppear {
                withAnimation(
                    Animation.linear(duration: duration)
                        .repeatForever(autoreverses: bounce)
                ) {
                    phase = 200
                }
            }
    }
}

extension View {
    func shimmer(duration: Double = 1.5, bounce: Bool = false) -> some View {
        modifier(ShimmerEffect(duration: duration, bounce: bounce))
    }
}

// MARK: - Particle System
struct ParticleSystem: View {
    let particleCount: Int
    let colors: [Color]
    @State private var particles: [Particle] = []
    
    var body: some View {
        ZStack {
            ForEach(particles.indices, id: \.self) { index in
                Circle()
                    .fill(particles[index].color)
                    .frame(width: particles[index].size, height: particles[index].size)
                    .position(particles[index].position)
                    .opacity(particles[index].opacity)
                    .animation(
                        Animation.linear(duration: particles[index].lifetime)
                            .delay(particles[index].delay),
                        value: particles[index].position
                    )
            }
        }
        .onAppear {
            generateParticles()
        }
    }
    
    private func generateParticles() {
        particles = (0..<particleCount).map { index in
            Particle(
                position: CGPoint(x: CGFloat.random(in: 0...300), y: 300),
                color: colors.randomElement() ?? .blue,
                size: CGFloat.random(in: 2...6),
                opacity: Double.random(in: 0.3...1.0),
                lifetime: Double.random(in: 2...5),
                delay: Double(index) * 0.1
            )
        }
        
        // Animate particles upward
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            for index in particles.indices {
                particles[index].position.y = -50
                particles[index].opacity = 0
            }
        }
    }
}

struct Particle {
    var position: CGPoint
    let color: Color
    let size: CGFloat
    var opacity: Double
    let lifetime: Double
    let delay: Double
}

// MARK: - Number Counter Animation
struct AnimatedNumber: View {
    let value: Double
    let formatter: (Double) -> String
    @State private var displayValue: Double = 0
    
    init(value: Double, formatter: @escaping (Double) -> String = { GameConstants.formatNumber($0) }) {
        self.value = value
        self.formatter = formatter
    }
    
    var body: some View {
        Text(formatter(displayValue))
            .font(.system(.body, design: .monospaced))
            .contentTransition(.numericText())
            .animation(.easeInOut(duration: 0.5), value: displayValue)
            .onAppear {
                displayValue = value
            }
            .onChange(of: value) { newValue in
                withAnimation(.easeInOut(duration: 0.3)) {
                    displayValue = newValue
                }
            }
    }
}

// MARK: - Success Feedback
struct SuccessFeedback: ViewModifier {
    @State private var showSuccess = false
    let trigger: Bool
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.system(size: 24))
                    .scaleEffect(showSuccess ? 1.2 : 0.1)
                    .opacity(showSuccess ? 1 : 0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.6), value: showSuccess)
            )
            .onChange(of: trigger) { _ in
                showSuccess = true
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    showSuccess = false
                }
            }
    }
}

extension View {
    func successFeedback(trigger: Bool) -> some View {
        modifier(SuccessFeedback(trigger: trigger))
    }
}

// MARK: - Responsive Design Helpers
struct ResponsiveLayout {
    static func gridColumns(for size: CGSize) -> Int {
        if size.width > 1000 { // iPad landscape
            return 8
        } else if size.width > 700 { // iPad portrait
            return 6
        } else { // iPhone
            return 4
        }
    }
    
    static func cellSize(for size: CGSize) -> CGFloat {
        if size.width > 1000 {
            return 80
        } else if size.width > 700 {
            return 70
        } else {
            return 60
        }
    }
    
    static func resourceCardHeight(for size: CGSize) -> CGFloat {
        return size.height * (size.width > 700 ? 0.12 : 0.15)
    }
}

// MARK: - Theme Colors
extension Color {
    static let datacenterBlue = Color(red: 0.2, green: 0.4, blue: 0.8)
    static let datacenterGreen = Color(red: 0.2, green: 0.8, blue: 0.4)
    static let datacenterOrange = Color(red: 1.0, green: 0.6, blue: 0.2)
    static let datacenterPurple = Color(red: 0.6, green: 0.2, blue: 0.8)
    static let datacenterBackground = Color(red: 0.95, green: 0.97, blue: 1.0)
}
