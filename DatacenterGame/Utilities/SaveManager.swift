import Foundation
import UIKit
import UserNotifications

class SaveManager {
    static let shared = SaveManager()
    
    private let gameStateKey = "DatacenterGame_GameState"
    private let userDefaults = UserDefaults.standard
    
    private init() {}
    
    // MARK: - Save Game State
    func saveGameState(_ gameState: GameState) {
        do {
            // Update last save time
            gameState.lastSaveTime = Date()
            
            // Encode game state to JSON
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(gameState)
            
            // Save to UserDefaults
            userDefaults.set(data, forKey: gameStateKey)
            userDefaults.synchronize()
            
            print("Game state saved successfully")
        } catch {
            print("Failed to save game state: \(error)")
        }
    }
    
    // MARK: - Load Game State
    func loadGameState(_ gameState: GameState) {
        guard let data = userDefaults.data(forKey: gameStateKey) else {
            print("No saved game state found, starting new game")
            return
        }
        
        do {
            // Decode game state from JSON
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            let savedState = try decoder.decode(GameState.self, from: data)
            
            // Copy saved data to current game state
            gameState.currentResources = savedState.currentResources
            gameState.placedEquipment = savedState.placedEquipment
            gameState.lastSaveTime = savedState.lastSaveTime
            gameState.totalRevenue = savedState.totalRevenue
            gameState.gameStartTime = savedState.gameStartTime
            gameState.gridSize = savedState.gridSize
            
            print("Game state loaded successfully")
        } catch {
            print("Failed to load game state: \(error)")
            // If loading fails, start with default state
        }
    }
    
    // MARK: - Clear Save Data
    func clearSaveData() {
        userDefaults.removeObject(forKey: gameStateKey)
        userDefaults.synchronize()
        print("Save data cleared")
    }
    
    // MARK: - Check if Save Exists
    func hasSaveData() -> Bool {
        return userDefaults.data(forKey: gameStateKey) != nil
    }
    
    // MARK: - Auto-save functionality
    func startAutoSave(for gameState: GameState, interval: TimeInterval = 30.0) {
        Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { _ in
            self.saveGameState(gameState)
        }
    }
}

// MARK: - Game Timer Utility
class GameTimer {
    private var timer: Timer?
    private let interval: TimeInterval
    private let action: () -> Void
    
    init(interval: TimeInterval, action: @escaping () -> Void) {
        self.interval = interval
        self.action = action
    }
    
    func start() {
        stop() // Stop any existing timer
        timer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { _ in
            self.action()
        }
    }
    
    func stop() {
        timer?.invalidate()
        timer = nil
    }
    
    var isRunning: Bool {
        return timer?.isValid ?? false
    }
}

// MARK: - Background Task Manager
class BackgroundTaskManager {
    private var backgroundTaskID: UIBackgroundTaskIdentifier = .invalid
    
    func beginBackgroundTask() {
        backgroundTaskID = UIApplication.shared.beginBackgroundTask(withName: "DatacenterGameSave") {
            self.endBackgroundTask()
        }
    }
    
    func endBackgroundTask() {
        if backgroundTaskID != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTaskID)
            backgroundTaskID = .invalid
        }
    }
}

// MARK: - Notification Manager
class NotificationManager {
    static let shared = NotificationManager()
    
    private init() {}
    
    func requestPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if granted {
                print("Notification permission granted")
            } else if let error = error {
                print("Notification permission error: \(error)")
            }
        }
    }
    
    func scheduleOfflineEarningsNotification(after timeInterval: TimeInterval) {
        let content = UNMutableNotificationContent()
        content.title = "Datacenter Tycoon"
        content.body = "Your datacenter has been generating revenue while you were away!"
        content.sound = .default
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: timeInterval, repeats: false)
        let request = UNNotificationRequest(identifier: "offline_earnings", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to schedule notification: \(error)")
            }
        }
    }
    
    func cancelAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
    }
}
