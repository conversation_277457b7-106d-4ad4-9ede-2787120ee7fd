import Foundation
import SwiftUI

class DatacenterManager: ObservableObject {
    @Published var gameState: GameState?
    @Published var isBoostActive: Bool = false
    
    private var gameTimer: Timer?
    private var boostTimer: Timer?
    private var lastUpdateTime: Date = Date()
    
    init() {
        startGameTimer()
    }
    
    deinit {
        stopGameTimer()
    }
    
    // MARK: - Timer Management
    func startGameTimer() {
        stopGameTimer()
        gameTimer = Timer.scheduledTimer(withTimeInterval: GameConstants.resourceGenerationInterval, repeats: true) { _ in
            self.updateResources()
        }
    }
    
    func stopGameTimer() {
        gameTimer?.invalidate()
        gameTimer = nil
    }
    
    // MARK: - Resource Management
    func updateResources() {
        guard let gameState = gameState else { return }
        
        let currentTime = Date()
        let deltaTime = currentTime.timeIntervalSince(lastUpdateTime)
        lastUpdateTime = currentTime
        
        // Calculate net resource generation
        let production = gameState.calculateTotalProduction()
        let consumption = gameState.calculateTotalConsumption()
        let netGeneration = calculateNetGeneration(production: production, consumption: consumption)
        
        // Apply boost multiplier if active
        let finalGeneration = isBoostActive ? netGeneration * GameConstants.tapBoostMultiplier : netGeneration
        
        // Update resources based on time elapsed
        let resourceDelta = finalGeneration * deltaTime
        gameState.currentResources = gameState.currentResources + resourceDelta
        
        // Ensure resources don't go negative (except revenue which can be spent)
        gameState.currentResources.power = max(0, gameState.currentResources.power)
        gameState.currentResources.cooling = max(0, gameState.currentResources.cooling)
        gameState.currentResources.network = max(0, gameState.currentResources.network)
        gameState.currentResources.storage = max(0, gameState.currentResources.storage)
        
        // Track total revenue earned
        if resourceDelta.revenue > 0 {
            gameState.totalRevenue += resourceDelta.revenue
        }
    }
    
    private func calculateNetGeneration(production: ResourceAmount, consumption: ResourceAmount) -> ResourceAmount {
        guard let gameState = gameState else { return ResourceAmount() }
        
        var netGeneration = production - consumption
        
        // Servers only generate revenue if they have adequate power and cooling
        let serverCount = Double(gameState.getEquipmentCount(type: .server))
        let availablePower = gameState.currentResources.power + netGeneration.power
        let availableCooling = gameState.currentResources.cooling + netGeneration.cooling
        
        // Calculate efficiency based on resource availability
        let powerEfficiency = min(1.0, availablePower / max(1.0, consumption.power))
        let coolingEfficiency = min(1.0, availableCooling / max(1.0, consumption.cooling))
        let overallEfficiency = min(powerEfficiency, coolingEfficiency)
        
        // Apply efficiency to revenue generation
        netGeneration.revenue *= overallEfficiency
        
        return netGeneration
    }
    
    // MARK: - Equipment Management
    func purchaseEquipment(_ definition: EquipmentDefinition, at position: GridPosition) -> Bool {
        guard let gameState = gameState else { return false }
        
        let success = gameState.purchaseEquipment(definition, at: position)
        if success {
            // Trigger haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
        return success
    }
    
    func removeEquipment(at position: GridPosition) {
        gameState?.removeEquipment(at: position)
    }
    
    func getAvailableEquipment() -> [EquipmentDefinition] {
        guard let gameState = gameState else { return [] }
        return GameConstants.getAvailableEquipment(for: gameState)
    }
    
    // MARK: - Boost System
    func activateBoost() {
        isBoostActive = true
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
        
        // Stop any existing boost timer
        boostTimer?.invalidate()
        
        // Start new boost timer
        boostTimer = Timer.scheduledTimer(withTimeInterval: GameConstants.tapBoostDuration, repeats: false) { _ in
            self.isBoostActive = false
        }
    }
    
    // MARK: - App Lifecycle
    func handleAppBecomeActive() {
        guard let gameState = gameState else { return }
        
        let currentTime = Date()
        let timeAway = currentTime.timeIntervalSince(gameState.lastSaveTime)
        
        // Cap offline earnings to prevent exploitation
        let maxOfflineTime = GameConstants.maxOfflineHours * 3600 // Convert hours to seconds
        let offlineTime = min(timeAway, maxOfflineTime)
        
        if offlineTime > 60 { // Only show offline earnings if away for more than 1 minute
            calculateOfflineEarnings(timeAway: offlineTime)
        }
        
        lastUpdateTime = currentTime
        startGameTimer()
    }
    
    private func calculateOfflineEarnings(timeAway: TimeInterval) {
        guard let gameState = gameState else { return }
        
        let production = gameState.calculateTotalProduction()
        let consumption = gameState.calculateTotalConsumption()
        let netGeneration = calculateNetGeneration(production: production, consumption: consumption)
        
        // Only apply positive revenue generation for offline earnings
        if netGeneration.revenue > 0 {
            let offlineEarnings = netGeneration.revenue * timeAway
            gameState.currentResources.revenue += offlineEarnings
            gameState.totalRevenue += offlineEarnings
            
            // Schedule offline earnings notification
            NotificationManager.shared.scheduleOfflineEarningsNotification(after: timeAway)
        }
    }
    
    // MARK: - Statistics
    func getGameStatistics() -> GameStatistics {
        guard let gameState = gameState else {
            return GameStatistics()
        }
        
        let production = gameState.calculateTotalProduction()
        let consumption = gameState.calculateTotalConsumption()
        let netGeneration = calculateNetGeneration(production: production, consumption: consumption)
        
        return GameStatistics(
            totalRevenue: gameState.totalRevenue,
            revenuePerSecond: netGeneration.revenue,
            totalEquipment: gameState.placedEquipment.count,
            gameTime: Date().timeIntervalSince(gameState.gameStartTime),
            efficiency: calculateOverallEfficiency()
        )
    }
    
    private func calculateOverallEfficiency() -> Double {
        guard let gameState = gameState else { return 0 }
        
        let production = gameState.calculateTotalProduction()
        let consumption = gameState.calculateTotalConsumption()
        
        if consumption.power == 0 && consumption.cooling == 0 {
            return 1.0
        }
        
        let powerEfficiency = gameState.currentResources.power / max(1.0, consumption.power)
        let coolingEfficiency = gameState.currentResources.cooling / max(1.0, consumption.cooling)
        
        return min(1.0, min(powerEfficiency, coolingEfficiency))
    }
}

struct GameStatistics {
    let totalRevenue: Double
    let revenuePerSecond: Double
    let totalEquipment: Int
    let gameTime: TimeInterval
    let efficiency: Double
    
    init() {
        self.totalRevenue = 0
        self.revenuePerSecond = 0
        self.totalEquipment = 0
        self.gameTime = 0
        self.efficiency = 0
    }
    
    init(totalRevenue: Double, revenuePerSecond: Double, totalEquipment: Int, gameTime: TimeInterval, efficiency: Double) {
        self.totalRevenue = totalRevenue
        self.revenuePerSecond = revenuePerSecond
        self.totalEquipment = totalEquipment
        self.gameTime = gameTime
        self.efficiency = efficiency
    }
}
