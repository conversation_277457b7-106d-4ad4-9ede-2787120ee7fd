import Foundation
import SwiftUI

class GameState: ObservableObject, Codable {
    @Published var currentResources: ResourceAmount = ResourceAmount()
    @Published var placedEquipment: [PlacedEquipment] = []
    @Published var lastSaveTime: Date = Date()
    @Published var totalRevenue: Double = 0
    @Published var gameStartTime: Date = Date()
    @Published var gridSize: GridSize = GridSize(width: 6, height: 6)
    
    // Starting resources
    init() {
        currentResources.revenue = 100 // Starting money
        currentResources.power = 10   // Starting power
        currentResources.cooling = 10 // Starting cooling
    }
    
    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case currentResources
        case placedEquipment
        case lastSaveTime
        case totalRevenue
        case gameStartTime
        case gridSize
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        currentResources = try container.decode(ResourceAmount.self, forKey: .currentResources)
        placedEquipment = try container.decode([PlacedEquipment].self, forKey: .placedEquipment)
        lastSaveTime = try container.decode(Date.self, forKey: .lastSaveTime)
        totalRevenue = try container.decode(Double.self, forKey: .totalRevenue)
        gameStartTime = try container.decode(Date.self, forKey: .gameStartTime)
        gridSize = try container.decode(GridSize.self, forKey: .gridSize)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(currentResources, forKey: .currentResources)
        try container.encode(placedEquipment, forKey: .placedEquipment)
        try container.encode(lastSaveTime, forKey: .lastSaveTime)
        try container.encode(totalRevenue, forKey: .totalRevenue)
        try container.encode(gameStartTime, forKey: .gameStartTime)
        try container.encode(gridSize, forKey: .gridSize)
    }
    
    // MARK: - Game Logic Methods
    func canAfford(_ cost: Double) -> Bool {
        return currentResources.revenue >= cost
    }
    
    func canPlaceEquipment(at position: GridPosition) -> Bool {
        return !placedEquipment.contains { $0.position == position } &&
               position.x >= 0 && position.x < gridSize.width &&
               position.y >= 0 && position.y < gridSize.height
    }
    
    func purchaseEquipment(_ definition: EquipmentDefinition, at position: GridPosition) -> Bool {
        guard canAfford(definition.cost) && canPlaceEquipment(at: position) else {
            return false
        }
        
        currentResources.revenue -= definition.cost
        let equipment = PlacedEquipment(definition: definition, position: position)
        placedEquipment.append(equipment)
        return true
    }
    
    func removeEquipment(at position: GridPosition) {
        placedEquipment.removeAll { $0.position == position }
    }
    
    func getEquipment(at position: GridPosition) -> PlacedEquipment? {
        return placedEquipment.first { $0.position == position }
    }
    
    func getEquipmentCount(type: EquipmentType) -> Int {
        return placedEquipment.filter { $0.definition.type == type }.count
    }
    
    // Calculate total production and consumption
    func calculateTotalProduction() -> ResourceAmount {
        var total = ResourceAmount()
        for equipment in placedEquipment where equipment.isOperational {
            total = total + equipment.definition.actualProduction
        }
        return total
    }
    
    func calculateTotalConsumption() -> ResourceAmount {
        var total = ResourceAmount()
        for equipment in placedEquipment where equipment.isOperational {
            total = total + equipment.definition.actualConsumption
        }
        return total
    }
}

struct GridSize: Codable {
    let width: Int
    let height: Int
}
