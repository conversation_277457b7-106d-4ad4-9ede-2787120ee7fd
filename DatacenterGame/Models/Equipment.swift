import Foundation
import SwiftUI

enum EquipmentType: String, CaseIterable, Codable {
    case server = "server"
    case coolingUnit = "cooling_unit"
    case powerSupply = "power_supply"
    case networkSwitch = "network_switch"
    
    var displayName: String {
        switch self {
        case .server: return "Server"
        case .coolingUnit: return "Cooling Unit"
        case .powerSupply: return "Power Supply"
        case .networkSwitch: return "Network Switch"
        }
    }
    
    var icon: String {
        switch self {
        case .server: return "server.rack"
        case .coolingUnit: return "air.conditioner.horizontal"
        case .powerSupply: return "battery.100"
        case .networkSwitch: return "router"
        }
    }
    
    var color: Color {
        switch self {
        case .server: return .gray
        case .coolingUnit: return .blue
        case .powerSupply: return .yellow
        case .networkSwitch: return .green
        }
    }
}

enum EquipmentTier: String, CaseIterable, Codable {
    case basic = "basic"
    case advanced = "advanced"
    case enterprise = "enterprise"
    case quantum = "quantum"
    
    var displayName: String {
        switch self {
        case .basic: return "Basic"
        case .advanced: return "Advanced"
        case .enterprise: return "Enterprise"
        case .quantum: return "Quantum"
        }
    }
    
    var multiplier: Double {
        switch self {
        case .basic: return 1.0
        case .advanced: return 2.5
        case .enterprise: return 6.0
        case .quantum: return 15.0
        }
    }
    
    var costMultiplier: Double {
        switch self {
        case .basic: return 1.0
        case .advanced: return 4.0
        case .enterprise: return 15.0
        case .quantum: return 50.0
        }
    }
}

struct EquipmentDefinition: Codable {
    let type: EquipmentType
    let tier: EquipmentTier
    let baseCost: Double
    let produces: ResourceAmount
    let consumes: ResourceAmount
    let unlockRequirement: ResourceAmount
    
    var cost: Double {
        return baseCost * tier.costMultiplier
    }
    
    var actualProduction: ResourceAmount {
        return produces * tier.multiplier
    }
    
    var actualConsumption: ResourceAmount {
        return consumes * tier.multiplier
    }
    
    var displayName: String {
        return "\(tier.displayName) \(type.displayName)"
    }
}

struct PlacedEquipment: Codable, Identifiable {
    let id = UUID()
    let definition: EquipmentDefinition
    let position: GridPosition
    var purchaseTime: Date = Date()
    
    var isOperational: Bool {
        // Equipment becomes operational after a brief delay
        return Date().timeIntervalSince(purchaseTime) > 1.0
    }
}

struct GridPosition: Codable, Hashable {
    let x: Int
    let y: Int
    
    static let zero = GridPosition(x: 0, y: 0)
}
