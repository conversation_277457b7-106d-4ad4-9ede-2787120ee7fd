import Foundation
import SwiftUI

enum ResourceType: String, CaseIterable, Codable {
    case power = "power"
    case cooling = "cooling"
    case network = "network"
    case storage = "storage"
    case revenue = "revenue"
    
    var displayName: String {
        switch self {
        case .power: return "Power"
        case .cooling: return "Cooling"
        case .network: return "Network"
        case .storage: return "Storage"
        case .revenue: return "Revenue"
        }
    }
    
    var icon: String {
        switch self {
        case .power: return "bolt.fill"
        case .cooling: return "snowflake"
        case .network: return "network"
        case .storage: return "externaldrive.fill"
        case .revenue: return "dollarsign.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .power: return .yellow
        case .cooling: return .blue
        case .network: return .green
        case .storage: return .purple
        case .revenue: return .orange
        }
    }
    
    var unit: String {
        switch self {
        case .power: return "kW"
        case .cooling: return "BTU"
        case .network: return "Mbps"
        case .storage: return "TB"
        case .revenue: return "$"
        }
    }
}

struct ResourceAmount: Codable {
    var power: Double = 0
    var cooling: Double = 0
    var network: Double = 0
    var storage: Double = 0
    var revenue: Double = 0
    
    subscript(resourceType: ResourceType) -> Double {
        get {
            switch resourceType {
            case .power: return power
            case .cooling: return cooling
            case .network: return network
            case .storage: return storage
            case .revenue: return revenue
            }
        }
        set {
            switch resourceType {
            case .power: power = newValue
            case .cooling: cooling = newValue
            case .network: network = newValue
            case .storage: storage = newValue
            case .revenue: revenue = newValue
            }
        }
    }
    
    static func + (lhs: ResourceAmount, rhs: ResourceAmount) -> ResourceAmount {
        return ResourceAmount(
            power: lhs.power + rhs.power,
            cooling: lhs.cooling + rhs.cooling,
            network: lhs.network + rhs.network,
            storage: lhs.storage + rhs.storage,
            revenue: lhs.revenue + rhs.revenue
        )
    }
    
    static func - (lhs: ResourceAmount, rhs: ResourceAmount) -> ResourceAmount {
        return ResourceAmount(
            power: lhs.power - rhs.power,
            cooling: lhs.cooling - rhs.cooling,
            network: lhs.network - rhs.network,
            storage: lhs.storage - rhs.storage,
            revenue: lhs.revenue - rhs.revenue
        )
    }
    
    static func * (lhs: ResourceAmount, multiplier: Double) -> ResourceAmount {
        return ResourceAmount(
            power: lhs.power * multiplier,
            cooling: lhs.cooling * multiplier,
            network: lhs.network * multiplier,
            storage: lhs.storage * multiplier,
            revenue: lhs.revenue * multiplier
        )
    }
}
