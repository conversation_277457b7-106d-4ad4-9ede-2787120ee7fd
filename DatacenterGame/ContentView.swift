import SwiftUI

struct ContentView: View {
    @EnvironmentObject var gameState: GameState
    @EnvironmentObject var datacenterManager: DatacenterManager
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // Resource Display Header
                ResourceDisplayView()
                    .frame(height: ResponsiveLayout.resourceCardHeight(for: geometry.size))
                    .background(Color.datacenterBackground)

                // Main Game Area
                GameView()
                    .frame(height: geometry.size.height * 0.7)

                // Equipment Purchase Area
                EquipmentPurchaseView()
                    .frame(height: geometry.size.height * 0.15)
                    .background(Color.gray.opacity(0.1))
            }
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .ignoresSafeArea(.all, edges: .bottom)
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .environmentObject(GameState())
            .environmentObject(DatacenterManager())
    }
}
