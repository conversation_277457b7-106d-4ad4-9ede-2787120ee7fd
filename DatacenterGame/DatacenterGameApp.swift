import SwiftUI

@main
struct DatacenterGameApp: App {
    @StateObject private var gameState = GameState()
    @StateObject private var datacenterManager = DatacenterManager()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(gameState)
                .environmentObject(datacenterManager)
                .onAppear {
                    // Load saved game state
                    SaveManager.shared.loadGameState(gameState)
                    datacenterManager.gameState = gameState
                    datacenterManager.startGameTimer()
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)) { _ in
                    // Save game state when app goes to background
                    SaveManager.shared.saveGameState(gameState)
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
                    // Handle returning from background
                    datacenterManager.handleAppBecomeActive()
                }
        }
    }
}
